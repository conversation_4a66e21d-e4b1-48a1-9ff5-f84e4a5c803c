#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动操作版本的设备明细列表数据爬虫
使用requests和BeautifulSoup，需要用户手动获取数据
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import json
import time

def manual_scrape():
    """手动爬取指导"""
    print("设备明细列表数据爬虫 - 手动版本")
    print("="*50)
    print()
    print("由于浏览器驱动问题，我们将使用手动方式获取数据。")
    print("请按照以下步骤操作：")
    print()
    print("步骤1: 打开浏览器")
    print("  - 打开Edge或Chrome浏览器")
    print("  - 访问: https://open.dianxinai.com/home?route=MyBill")
    print("  - 完成登录")
    print()
    print("步骤2: 获取数据")
    print("  - 进入设备明细列表页面")
    print("  - 按F12打开开发者工具")
    print("  - 点击Network(网络)标签")
    print("  - 刷新页面或翻页")
    print("  - 查找包含设备数据的API请求")
    print()
    print("步骤3: 复制数据")
    print("  - 找到返回设备列表的API请求")
    print("  - 右键点击该请求 -> Copy -> Copy Response")
    print("  - 将数据粘贴到下面")
    print()
    
    all_data = []
    page_num = 1
    
    while True:
        print(f"\n第 {page_num} 页数据:")
        print("请粘贴API响应数据 (直接按回车结束):")
        
        lines = []
        while True:
            line = input()
            if line.strip() == "":
                break
            lines.append(line)
        
        if not lines:
            break
        
        response_text = "\n".join(lines)
        
        try:
            # 尝试解析JSON数据
            data = json.loads(response_text)
            
            # 提取设备数据 (需要根据实际API结构调整)
            devices = []
            if isinstance(data, dict):
                # 常见的数据结构
                possible_keys = ['data', 'result', 'list', 'items', 'records', 'content']
                for key in possible_keys:
                    if key in data:
                        if isinstance(data[key], list):
                            devices = data[key]
                            break
                        elif isinstance(data[key], dict):
                            for sub_key in possible_keys:
                                if sub_key in data[key] and isinstance(data[key][sub_key], list):
                                    devices = data[key][sub_key]
                                    break
            elif isinstance(data, list):
                devices = data
            
            if devices:
                print(f"解析到 {len(devices)} 条设备数据")
                all_data.extend(devices)
            else:
                print("未找到设备数据，请检查API响应格式")
                
        except json.JSONDecodeError:
            print("数据格式不是有效的JSON，请检查复制的内容")
        except Exception as e:
            print(f"解析数据时出错: {e}")
        
        page_num += 1
        print(f"当前总计: {len(all_data)} 条数据")
        
        continue_input = input("是否继续添加下一页数据? (y/n): ").strip().lower()
        if continue_input not in ['y', 'yes', '是']:
            break
    
    if all_data:
        save_to_excel(all_data)
    else:
        print("没有获取到任何数据")

def save_to_excel(data, filename="设备明细列表.xlsx"):
    """保存数据到Excel"""
    try:
        if not data:
            print("没有数据可保存")
            return False
        
        # 如果数据是字典列表，直接转换
        if isinstance(data[0], dict):
            df = pd.DataFrame(data)
        else:
            # 如果是其他格式，尝试转换
            df = pd.DataFrame(data)
        
        # 保存到Excel
        df.to_excel(filename, index=False, engine='openpyxl')
        print(f"✓ 数据已保存到 {filename}")
        print(f"✓ 总共保存了 {len(data)} 条记录")
        print(f"✓ 包含 {len(df.columns)} 个字段: {list(df.columns)}")
        return True
        
    except Exception as e:
        print(f"保存Excel文件时出错: {e}")
        return False

def create_sample_excel():
    """创建示例Excel文件"""
    print("\n创建示例Excel文件...")
    
    # 示例数据
    sample_data = [
        {
            "设备名称": "示例设备1",
            "设备型号": "Model-001", 
            "设备状态": "正常",
            "创建时间": "2024-01-01",
            "备注": "示例数据"
        },
        {
            "设备名称": "示例设备2",
            "设备型号": "Model-002",
            "设备状态": "维护中", 
            "创建时间": "2024-01-02",
            "备注": "示例数据"
        }
    ]
    
    save_to_excel(sample_data, "示例_设备明细列表.xlsx")

def main():
    """主函数"""
    print("选择操作模式:")
    print("1. 手动爬取数据")
    print("2. 创建示例Excel文件")
    print("3. 查看详细说明")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == "1":
        manual_scrape()
    elif choice == "2":
        create_sample_excel()
    elif choice == "3":
        show_detailed_instructions()
    else:
        print("无效选择")

def show_detailed_instructions():
    """显示详细说明"""
    print("\n详细操作说明:")
    print("="*50)
    print()
    print("方法1: 使用浏览器开发者工具")
    print("1. 打开 https://open.dianxinai.com/home?route=MyBill")
    print("2. 登录账户")
    print("3. 按F12打开开发者工具")
    print("4. 点击Network标签")
    print("5. 刷新页面，观察网络请求")
    print("6. 找到返回设备数据的API请求")
    print("7. 右键该请求 -> Copy -> Copy Response")
    print("8. 运行此脚本，选择模式1，粘贴数据")
    print()
    print("方法2: 直接复制表格数据")
    print("1. 在网页上选中整个设备表格")
    print("2. 复制到Excel中")
    print("3. 手动整理数据格式")
    print()
    print("方法3: 使用浏览器插件")
    print("1. 安装Table Capture等浏览器插件")
    print("2. 使用插件导出表格数据")
    print()

if __name__ == "__main__":
    main()
