# 设备明细列表爬虫

这个爬虫用于爬取 https://open.dianxinai.com/home?route=MyBill 页面的设备明细列表数据，并保存到Excel文件中。

## 功能特点

- 自动爬取所有页面的设备数据
- 支持手动登录（如果网站需要登录）
- 自动处理分页
- 将数据保存为Excel格式
- 详细的日志记录

## 安装依赖

### 方法1：使用安装脚本（推荐）
```bash
python install_dependencies.py
```

### 方法2：手动安装
```bash
pip install -r requirements.txt
```

### 方法3：逐个安装
```bash
pip install selenium==4.15.0
pip install pandas==2.1.3
pip install openpyxl==3.1.2
pip install webdriver-manager==4.0.1
```

## 使用方法

1. 确保已安装所有依赖
2. 运行爬虫：
   ```bash
   python device_scraper.py
   ```

3. 如果网站需要登录：
   - 爬虫会自动打开浏览器
   - 在浏览器中手动完成登录
   - 回到命令行按回车键继续

4. 爬虫会自动：
   - 查找设备明细表格
   - 爬取所有页面的数据
   - 保存到 `设备明细列表.xlsx` 文件

## 文件说明

- `device_scraper.py` - 主爬虫脚本
- `install_dependencies.py` - 依赖安装脚本
- `requirements.txt` - 依赖列表
- `README.md` - 使用说明

## 注意事项

1. **Chrome浏览器**: 需要安装Chrome浏览器，脚本会自动下载对应的ChromeDriver
2. **网络连接**: 确保网络连接正常
3. **登录**: 如果网站需要登录，请在浏览器中手动完成登录
4. **反爬虫**: 如果遇到反爬虫机制，可能需要调整爬取间隔时间

## 故障排除

### 1. Chrome驱动问题
如果遇到Chrome驱动相关错误：
- 确保已安装Chrome浏览器
- 检查网络连接，webdriver-manager需要下载驱动

### 2. 找不到表格数据
如果爬虫找不到表格：
- 检查网站是否需要登录
- 确认页面已完全加载
- 网站结构可能发生变化，需要更新选择器

### 3. 分页问题
如果无法翻页：
- 检查网站的分页按钮是否发生变化
- 可能需要手动调整分页选择器

## 自定义配置

可以在 `device_scraper.py` 中修改以下参数：

- `headless=True` - 设置为无头模式（不显示浏览器窗口）
- 等待时间 - 调整 `time.sleep()` 的值
- 选择器 - 根据网站结构调整CSS选择器

## 输出文件

爬取完成后会生成 `设备明细列表.xlsx` 文件，包含所有页面的设备数据。
