#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速设备数据爬虫
基于您提供的API响应格式，快速爬取1-952页的所有数据
"""

import requests
import pandas as pd
import time
import json
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickDeviceScraper:
    def __init__(self):
        """初始化快速爬虫"""
        self.session = requests.Session()
        self.all_data = []
        self.base_url = ""
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://open.dianxinai.com/',
            'Content-Type': 'application/json'
        })
    
    def get_api_info(self):
        """获取API信息"""
        print("🔧 快速设备数据爬虫")
        print("="*50)
        print()
        print("根据您提供的数据格式，我们需要以下信息：")
        print()
        
        # 获取API URL
        print("1. 请提供设备列表API的完整URL:")
        print("   (在浏览器开发者工具Network标签中找到)")
        api_url = input("   API URL: ").strip()
        
        if not api_url:
            print("❌ 需要提供API URL")
            return False
        
        self.base_url = api_url
        
        # 获取Cookie
        print("\n2. 请提供Cookie信息:")
        print("   (右键API请求 -> Copy -> Copy as cURL，提取Cookie部分)")
        cookie = input("   Cookie: ").strip()
        
        if cookie:
            self.session.headers['Cookie'] = cookie
        else:
            print("⚠️ 没有Cookie可能导致认证失败")
        
        # 获取其他可能需要的头信息
        print("\n3. 其他认证信息 (可选):")
        
        token = input("   Authorization Token (可选): ").strip()
        if token:
            self.session.headers['Authorization'] = token
        
        return True
    
    def test_single_request(self, page=2):
        """测试单个请求"""
        try:
            print(f"\n🔍 测试API请求 (第{page}页)...")
            
            # 根据您提供的格式，构建请求参数
            params = {
                'current': page,
                'size': 20
            }
            
            response = self.session.get(self.base_url, params=params, timeout=30)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    if data.get('code') == 200:
                        records = data.get('data', {}).get('records', [])
                        total = data.get('data', {}).get('total', 0)
                        pages = data.get('data', {}).get('pages', 0)
                        
                        print(f"✅ API测试成功!")
                        print(f"   总记录数: {total}")
                        print(f"   总页数: {pages}")
                        print(f"   当前页记录数: {len(records)}")
                        
                        if records:
                            print(f"   数据字段: {list(records[0].keys())}")
                            print(f"   示例记录: {records[0]}")
                        
                        return True, total, pages
                    else:
                        print(f"❌ API返回错误: code={data.get('code')}")
                        print(f"   响应: {data}")
                        return False, 0, 0
                        
                except json.JSONDecodeError:
                    print(f"❌ 响应不是有效的JSON格式")
                    print(f"   响应内容: {response.text[:500]}")
                    return False, 0, 0
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"   响应: {response.text[:500]}")
                return False, 0, 0
                
        except Exception as e:
            print(f"❌ 请求测试失败: {e}")
            return False, 0, 0
    
    def scrape_all_data(self, total_pages=952):
        """爬取所有数据"""
        print(f"\n🚀 开始爬取所有数据...")
        print(f"📄 总页数: {total_pages}")
        print(f"📊 预计记录数: {total_pages * 20}")
        print()
        
        success_count = 0
        failed_pages = []
        
        for page in range(1, total_pages + 1):
            try:
                # 构建请求参数
                params = {
                    'current': page,
                    'size': 20
                }
                
                response = self.session.get(self.base_url, params=params, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data.get('code') == 200:
                        records = data.get('data', {}).get('records', [])
                        
                        if records:
                            self.all_data.extend(records)
                            success_count += 1
                            
                            # 每50页显示一次进度
                            if page % 50 == 0:
                                print(f"📈 进度: {page}/{total_pages} ({page/total_pages*100:.1f}%) - 已获取 {len(self.all_data)} 条记录")
                        else:
                            failed_pages.append(page)
                            print(f"⚠️ 第{page}页无数据")
                    else:
                        failed_pages.append(page)
                        print(f"❌ 第{page}页API错误: {data.get('code')}")
                else:
                    failed_pages.append(page)
                    print(f"❌ 第{page}页HTTP错误: {response.status_code}")
                
                # 请求间隔，避免过于频繁
                time.sleep(0.3)
                
            except KeyboardInterrupt:
                print(f"\n⚠️ 用户中断，已爬取 {len(self.all_data)} 条记录")
                break
            except Exception as e:
                failed_pages.append(page)
                logger.error(f"第{page}页异常: {e}")
        
        print(f"\n📊 爬取完成!")
        print(f"✅ 成功页面: {success_count}")
        print(f"❌ 失败页面: {len(failed_pages)}")
        print(f"📋 总记录数: {len(self.all_data)}")
        
        if failed_pages and len(failed_pages) <= 20:
            print(f"⚠️ 失败页面: {failed_pages}")
        elif failed_pages:
            print(f"⚠️ 失败页面: {failed_pages[:10]}... (共{len(failed_pages)}页)")
        
        return self.all_data
    
    def save_to_excel(self, filename="设备明细列表_全部数据.xlsx"):
        """保存数据到Excel"""
        try:
            if not self.all_data:
                print("❌ 没有数据可保存")
                return False
            
            # 创建DataFrame
            df = pd.DataFrame(self.all_data)
            
            # 添加爬取时间
            df['数据爬取时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 重新排列列顺序，把重要信息放前面
            columns_order = ['sn', 'province', 'ispName', 'createTime', 'onlineDays', 'bandwidth', 'totalAmount', '数据爬取时间']
            df = df[columns_order]
            
            # 保存到Excel
            df.to_excel(filename, index=False, engine='openpyxl')
            
            print(f"✅ 数据已保存到: {filename}")
            print(f"📊 总记录数: {len(self.all_data)}")
            print(f"📋 数据字段: {list(df.columns)}")
            
            # 数据统计
            print(f"\n📈 数据统计:")
            print(f"   省份分布 (前5): {df['province'].value_counts().head().to_dict()}")
            print(f"   运营商分布: {df['ispName'].value_counts().to_dict()}")
            print(f"   总带宽: {df['bandwidth'].sum():.2f}")
            print(f"   总金额: {df['totalAmount'].sum():.2f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存Excel失败: {e}")
            return False

def main():
    """主函数"""
    scraper = QuickDeviceScraper()
    
    try:
        # 获取API信息
        if not scraper.get_api_info():
            return
        
        # 测试API
        success, total_records, total_pages = scraper.test_single_request()
        
        if not success:
            print("❌ API测试失败，请检查URL和认证信息")
            return
        
        # 确认爬取
        print(f"\n📋 准备爬取:")
        print(f"   总页数: {total_pages}")
        print(f"   总记录数: {total_records}")
        print(f"   预计时间: {total_pages * 0.3 / 60:.1f} 分钟")
        
        confirm = input(f"\n确认开始爬取全部 {total_pages} 页数据? (y/n): ").strip().lower()
        
        if confirm not in ['y', 'yes', '是']:
            print("已取消")
            return
        
        # 开始爬取
        data = scraper.scrape_all_data(total_pages)
        
        if data:
            # 保存数据
            scraper.save_to_excel()
            print(f"\n🎉 爬取完成! 共获取 {len(data)} 条设备数据")
            print(f"📁 文件已保存为: 设备明细列表_全部数据.xlsx")
        else:
            print("❌ 未获取到任何数据")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        if scraper.all_data:
            print(f"💾 保存已爬取的 {len(scraper.all_data)} 条数据...")
            scraper.save_to_excel("设备明细列表_部分数据.xlsx")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        logger.exception("详细错误信息:")

if __name__ == "__main__":
    main()
