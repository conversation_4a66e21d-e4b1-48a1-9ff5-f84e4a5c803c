#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备明细列表爬虫启动器
"""

import os
import sys
import subprocess

def check_dependencies():
    """检查依赖是否已安装"""
    required_packages = ['pandas', 'selenium', 'openpyxl', 'webdriver-manager', 'beautifulsoup4', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖包...")
    try:
        subprocess.run([sys.executable, "install_dependencies.py"], check=True)
        # 安装额外的包
        subprocess.run([sys.executable, "-m", "pip", "install", "beautifulsoup4", "requests"], check=True)
        print("✓ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🕷️  设备明细列表爬虫工具")
    print("="*60)
    print("📋 目标网站: https://open.dianxinai.com/home?route=MyBill")
    print("📁 输出文件: 设备明细列表.xlsx")
    print()
    print("请选择操作模式:")
    print()
    print("1. 🚀 快速API爬虫 (推荐)")
    print("   - 直接调用API接口")
    print("   - 可爬取全部952页数据")
    print("   - 速度快，效率高")
    print()
    print("2. 🔧 手动操作版本")
    print("   - 适合API配置困难的情况")
    print("   - 需要手动复制API数据")
    print("   - 兼容性最好")
    print()
    print("3. 🤖 自动化版本 (Edge浏览器)")
    print("   - 自动打开浏览器")
    print("   - 需要Edge浏览器")
    print("   - 可能需要配置驱动")
    print()
    print("4. 📖 查看详细说明")
    print("5. 🔧 安装/检查依赖")
    print("6. 📊 创建示例Excel文件")
    print("7. ❌ 退出")
    print()

def run_quick_scraper():
    """运行快速API爬虫"""
    print("\n启动快速API爬虫...")
    print("💡 此版本可以快速爬取全部952页数据")
    try:
        subprocess.run([sys.executable, "quick_scraper.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 快速爬虫运行失败")
    except KeyboardInterrupt:
        print("\n用户中断操作")

def run_manual_scraper():
    """运行手动爬虫"""
    print("\n启动手动操作版本...")
    try:
        subprocess.run([sys.executable, "manual_scraper.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 手动爬虫运行失败")
    except KeyboardInterrupt:
        print("\n用户中断操作")

def run_edge_scraper():
    """运行Edge自动化爬虫"""
    print("\n启动Edge自动化版本...")
    print("⚠️  注意：此版本需要Edge浏览器和对应驱动")
    confirm = input("确认继续？(y/n): ").strip().lower()

    if confirm in ['y', 'yes', '是']:
        try:
            subprocess.run([sys.executable, "edge_scraper.py"], check=True)
        except subprocess.CalledProcessError:
            print("❌ Edge爬虫运行失败")
            print("💡 建议使用快速API爬虫或手动操作版本")
        except KeyboardInterrupt:
            print("\n用户中断操作")
    else:
        print("已取消")

def show_instructions():
    """显示详细说明"""
    print("\n" + "="*60)
    print("📖 详细使用说明")
    print("="*60)
    print()
    print("🎯 目标：爬取设备明细列表数据并保存为Excel文件")
    print()
    print("📋 手动操作版本步骤：")
    print("1. 在浏览器中打开: https://open.dianxinai.com/home?route=MyBill")
    print("2. 完成登录")
    print("3. 进入设备明细列表页面")
    print("4. 按F12打开开发者工具")
    print("5. 点击Network(网络)标签")
    print("6. 刷新页面或翻页，观察网络请求")
    print("7. 找到返回设备数据的API请求")
    print("8. 右键该请求 -> Copy -> Copy Response")
    print("9. 运行手动爬虫，粘贴数据")
    print()
    print("🤖 自动化版本步骤：")
    print("1. 确保已安装Edge浏览器")
    print("2. 运行自动化爬虫")
    print("3. 在打开的浏览器中手动登录")
    print("4. 按回车键继续，程序自动爬取")
    print()
    print("💡 提示：")
    print("- 推荐使用手动操作版本，兼容性更好")
    print("- 如果自动化版本失败，请使用手动版本")
    print("- 确保网络连接正常")
    print("- 准备好登录账号和密码")
    print()

def create_sample():
    """创建示例文件"""
    print("\n创建示例Excel文件...")
    try:
        subprocess.run([sys.executable, "-c", """
import pandas as pd
sample_data = [
    {"设备名称": "示例设备1", "设备型号": "Model-001", "设备状态": "正常", "创建时间": "2024-01-01", "备注": "示例数据"},
    {"设备名称": "示例设备2", "设备型号": "Model-002", "设备状态": "维护中", "创建时间": "2024-01-02", "备注": "示例数据"}
]
df = pd.DataFrame(sample_data)
df.to_excel("示例_设备明细列表.xlsx", index=False, engine='openpyxl')
print("✓ 示例文件已创建: 示例_设备明细列表.xlsx")
"""], check=True)
    except subprocess.CalledProcessError:
        print("❌ 创建示例文件失败")

def main():
    """主函数"""
    # 检查依赖
    missing = check_dependencies()
    if missing:
        print(f"⚠️  缺少依赖包: {', '.join(missing)}")
        install_choice = input("是否自动安装？(y/n): ").strip().lower()
        if install_choice in ['y', 'yes', '是']:
            if not install_dependencies():
                print("请手动安装依赖后重试")
                return
        else:
            print("请先安装依赖包")
            return
    
    while True:
        show_menu()
        choice = input("请选择 (1-7): ").strip()

        if choice == "1":
            run_quick_scraper()
        elif choice == "2":
            run_manual_scraper()
        elif choice == "3":
            run_edge_scraper()
        elif choice == "4":
            show_instructions()
        elif choice == "5":
            missing = check_dependencies()
            if missing:
                print(f"缺少依赖: {', '.join(missing)}")
                install_dependencies()
            else:
                print("✓ 所有依赖已安装")
        elif choice == "6":
            create_sample()
        elif choice == "7":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
