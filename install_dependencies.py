#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装依赖脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package} 安装失败")
        return False

def main():
    """主函数"""
    print("开始安装爬虫所需依赖...")
    
    packages = [
        "selenium==4.15.0",
        "pandas==2.1.3", 
        "openpyxl==3.1.2",
        "webdriver-manager==4.0.1"
    ]
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("所有依赖安装成功！现在可以运行爬虫了。")
        print("运行命令: python device_scraper.py")
    else:
        print("部分依赖安装失败，请检查网络连接或手动安装。")

if __name__ == "__main__":
    main()
