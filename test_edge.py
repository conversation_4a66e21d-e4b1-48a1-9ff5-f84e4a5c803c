#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Edge浏览器连接
"""

from selenium import webdriver
from selenium.webdriver.edge.options import Options
import time

def test_edge():
    """测试Edge浏览器"""
    try:
        print("正在启动Edge浏览器...")
        
        # 设置Edge选项
        edge_options = Options()
        edge_options.add_experimental_option("detach", True)  # 保持浏览器打开
        
        # 尝试启动Edge
        driver = webdriver.Edge(options=edge_options)
        print("✓ Edge浏览器启动成功！")
        
        # 访问测试网站
        print("正在访问目标网站...")
        driver.get("https://open.dianxinai.com/home?route=MyBill")
        print("✓ 网站访问成功！")
        
        print("\n请在浏览器中完成登录，然后按回车键继续...")
        input()
        
        # 查找表格
        tables = driver.find_elements("tag name", "table")
        print(f"找到 {len(tables)} 个表格")
        
        if tables:
            table = tables[0]
            rows = table.find_elements("tag name", "tr")
            print(f"第一个表格有 {len(rows)} 行")
            
            if len(rows) > 0:
                first_row = rows[0]
                cells = first_row.find_elements("tag name", "td") + first_row.find_elements("tag name", "th")
                print(f"第一行有 {len(cells)} 列")
                
                if cells:
                    print("第一行内容:")
                    for i, cell in enumerate(cells[:5]):  # 只显示前5列
                        print(f"  列{i+1}: {cell.text}")
        
        print("\n测试完成！浏览器将保持打开状态。")
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

if __name__ == "__main__":
    test_edge()
