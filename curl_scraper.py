#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于cURL命令的设备数据爬虫
自动解析cURL命令并爬取所有数据
"""

import requests
import pandas as pd
import time
import json
from datetime import datetime
import re

class CurlDeviceScraper:
    def __init__(self):
        """初始化爬虫"""
        self.session = requests.Session()
        self.all_data = []
        self.api_url = "https://open.dianxinai.com/app/openPlateForm/deviceCashBillForYear"
        
        # 从您提供的cURL命令中提取的信息
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Origin': 'https://open.dianxinai.com',
            'Pragma': 'no-cache',
            'Referer': 'https://open.dianxinai.com/home?route=MyBill',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
            'X-Authorization': 'Bearer eyJhbGciOiJIUzM4NCJ9.eyJzdWIiOiIyNDIyNjMiLCJzY29wZXMiOltdLCJ1c2VyX2lkIjoyNDIyNjMsIm5pY2tuYW1lIjoi5p6X6JCMIiwiYXZhdGFyX3VybCI6Imh0dHBzOi8vdGhpcmR3eC5xbG9nby5jbi9tbW9wZW4vdmlfMzIvUTBqNFR3R1RmVElUTHJwb1VpYVpFMlBBV3V4b0R4WTFzUzZuRnM1c0RKbXoydHJZUzZOcFdGN212ekM0SnQyMVE1WFRDdzBoZzVja3VXVWJYOXF4WXp3LzEzMiIsImlzcyI6InN0YXJiaXQiLCJpYXQiOjE3NTQwMjQ5MzYsImV4cCI6MTc1NDYyOTczNn0.4miqvWQ789ZZfmT61xjaaCNfDheSw_rCVTdT4N4hk90fpJrG0frh18q-bWwL2K4-',
            'app-version': 'OPEN',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sign': 'c171301da383cadcff9c26b9e21b9977fe4d74dfcab693dc3af9f93d113c10c0',
            'timestamp': '1754026988'
        }
        
        self.session.headers.update(self.headers)
    
    def update_auth_info(self):
        """更新认证信息"""
        print("🔧 设备数据爬虫 - 基于您的cURL命令")
        print("="*60)
        print()
        print("我已经从您的cURL命令中提取了认证信息。")
        print("如果需要更新认证信息，请提供新的值：")
        print()
        
        # 更新Authorization
        new_auth = input("新的X-Authorization (按回车保持当前): ").strip()
        if new_auth:
            self.session.headers['X-Authorization'] = new_auth
        
        # 更新sign
        new_sign = input("新的sign (按回车保持当前): ").strip()
        if new_sign:
            self.session.headers['sign'] = new_sign
        
        # 更新timestamp
        new_timestamp = input("新的timestamp (按回车保持当前): ").strip()
        if new_timestamp:
            self.session.headers['timestamp'] = new_timestamp
        
        print("✅ 认证信息已更新")
    
    def test_api(self):
        """测试API连接"""
        print("\n🔍 测试API连接...")
        
        try:
            # 测试请求数据
            test_data = {
                "year": "2025",
                "pageNo": 2,
                "pageSize": 20,
                "sn": ""
            }
            
            response = self.session.post(self.api_url, json=test_data, timeout=30)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('code') == 200:
                    records = data.get('data', {}).get('records', [])
                    total = data.get('data', {}).get('total', 0)
                    pages = data.get('data', {}).get('pages', 0)
                    
                    print(f"✅ API连接成功!")
                    print(f"   总记录数: {total}")
                    print(f"   总页数: {pages}")
                    print(f"   当前页记录数: {len(records)}")
                    
                    if records:
                        print(f"   数据字段: {list(records[0].keys())}")
                    
                    return True, total, pages
                else:
                    print(f"❌ API返回错误: {data}")
                    return False, 0, 0
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应: {response.text[:500]}")
                return False, 0, 0
                
        except Exception as e:
            print(f"❌ API测试失败: {e}")
            return False, 0, 0
    
    def scrape_page(self, page_no):
        """爬取单页数据"""
        try:
            data = {
                "year": "2025",
                "pageNo": page_no,
                "pageSize": 20,
                "sn": ""
            }
            
            response = self.session.post(self.api_url, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('code') == 200:
                    records = result.get('data', {}).get('records', [])
                    return records
                else:
                    print(f"❌ 第{page_no}页API错误: {result}")
                    return []
            else:
                print(f"❌ 第{page_no}页HTTP错误: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 第{page_no}页请求失败: {e}")
            return []
    
    def scrape_all_data(self, total_pages):
        """爬取所有数据"""
        print(f"\n🚀 开始爬取所有数据...")
        print(f"📄 总页数: {total_pages}")
        print(f"📊 预计记录数: {total_pages * 20}")
        print()
        
        success_count = 0
        failed_pages = []
        
        for page in range(1, total_pages + 1):
            try:
                records = self.scrape_page(page)
                
                if records:
                    self.all_data.extend(records)
                    success_count += 1
                    
                    # 每100页显示一次进度
                    if page % 100 == 0:
                        print(f"📈 进度: {page}/{total_pages} ({page/total_pages*100:.1f}%) - 已获取 {len(self.all_data)} 条记录")
                else:
                    failed_pages.append(page)
                
                # 请求间隔
                time.sleep(0.2)
                
            except KeyboardInterrupt:
                print(f"\n⚠️ 用户中断，已爬取 {len(self.all_data)} 条记录")
                break
            except Exception as e:
                failed_pages.append(page)
                print(f"❌ 第{page}页异常: {e}")
        
        print(f"\n📊 爬取完成!")
        print(f"✅ 成功页面: {success_count}")
        print(f"❌ 失败页面: {len(failed_pages)}")
        print(f"📋 总记录数: {len(self.all_data)}")
        
        if failed_pages:
            print(f"⚠️ 失败页面: {failed_pages[:10]}{'...' if len(failed_pages) > 10 else ''}")
        
        return self.all_data
    
    def save_to_excel(self, filename="设备明细列表_完整数据.xlsx"):
        """保存数据到Excel"""
        try:
            if not self.all_data:
                print("❌ 没有数据可保存")
                return False
            
            # 创建DataFrame
            df = pd.DataFrame(self.all_data)
            
            # 添加爬取时间
            df['数据爬取时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 重新排列列顺序
            columns_order = ['sn', 'province', 'ispName', 'createTime', 'onlineDays', 'bandwidth', 'totalAmount', '数据爬取时间']
            df = df[columns_order]
            
            # 保存到Excel
            df.to_excel(filename, index=False, engine='openpyxl')
            
            print(f"✅ 数据已保存到: {filename}")
            print(f"📊 总记录数: {len(self.all_data)}")
            
            # 数据统计
            print(f"\n📈 数据统计:")
            print(f"   省份分布 (前5): {df['province'].value_counts().head().to_dict()}")
            print(f"   运营商分布: {df['ispName'].value_counts().to_dict()}")
            print(f"   总带宽: {df['bandwidth'].sum():.2f}")
            print(f"   总金额: {df['totalAmount'].sum():.2f}")
            print(f"   在线天数统计: 平均{df['onlineDays'].mean():.1f}天, 最大{df['onlineDays'].max()}天")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存Excel失败: {e}")
            return False

def main():
    """主函数"""
    scraper = CurlDeviceScraper()
    
    try:
        # 询问是否需要更新认证信息
        update_auth = input("是否需要更新认证信息? (y/n，默认n): ").strip().lower()
        if update_auth in ['y', 'yes', '是']:
            scraper.update_auth_info()
        
        # 测试API
        success, total_records, total_pages = scraper.test_api()
        
        if not success:
            print("❌ API测试失败，可能需要更新认证信息")
            return
        
        # 确认爬取
        print(f"\n📋 准备爬取:")
        print(f"   总页数: {total_pages}")
        print(f"   总记录数: {total_records}")
        print(f"   预计时间: {total_pages * 0.2 / 60:.1f} 分钟")
        
        confirm = input(f"\n确认开始爬取全部 {total_pages} 页数据? (y/n): ").strip().lower()
        
        if confirm not in ['y', 'yes', '是']:
            print("已取消")
            return
        
        # 开始爬取
        data = scraper.scrape_all_data(total_pages)
        
        if data:
            # 保存数据
            scraper.save_to_excel()
            print(f"\n🎉 爬取完成! 共获取 {len(data)} 条设备数据")
            print(f"📁 文件已保存为: 设备明细列表_完整数据.xlsx")
        else:
            print("❌ 未获取到任何数据")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        if scraper.all_data:
            print(f"💾 保存已爬取的 {len(scraper.all_data)} 条数据...")
            scraper.save_to_excel("设备明细列表_部分数据.xlsx")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
