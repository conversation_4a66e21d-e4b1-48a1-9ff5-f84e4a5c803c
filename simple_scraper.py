#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版设备明细列表数据爬虫
"""

import time
import pandas as pd
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import logging
import os
import zipfile
import platform

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_chromedriver():
    """下载正确版本的ChromeDriver"""
    try:
        # 检查是否已存在chromedriver
        if os.path.exists("chromedriver.exe"):
            logger.info("发现已存在的chromedriver.exe")
            return "chromedriver.exe"
        
        # 获取Chrome版本
        import subprocess
        try:
            result = subprocess.run(['reg', 'query', 'HKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon', '/v', 'version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[2]
                chrome_version = version_line.split()[-1]
                major_version = chrome_version.split('.')[0]
                logger.info(f"检测到Chrome版本: {chrome_version}")
            else:
                major_version = "131"  # 默认版本
                logger.warning("无法检测Chrome版本，使用默认版本")
        except:
            major_version = "131"
            logger.warning("无法检测Chrome版本，使用默认版本")
        
        # 下载对应版本的ChromeDriver
        url = f"https://storage.googleapis.com/chrome-for-testing-public/{major_version}.0.6778.85/win64/chromedriver-win64.zip"
        logger.info(f"下载ChromeDriver: {url}")
        
        response = requests.get(url)
        if response.status_code == 200:
            with open("chromedriver.zip", "wb") as f:
                f.write(response.content)
            
            # 解压
            with zipfile.ZipFile("chromedriver.zip", 'r') as zip_ref:
                zip_ref.extractall(".")
            
            # 移动文件
            if os.path.exists("chromedriver-win64/chromedriver.exe"):
                os.rename("chromedriver-win64/chromedriver.exe", "chromedriver.exe")
                os.rmdir("chromedriver-win64")
            
            os.remove("chromedriver.zip")
            logger.info("ChromeDriver下载并解压成功")
            return "chromedriver.exe"
        else:
            logger.error("ChromeDriver下载失败")
            return None
            
    except Exception as e:
        logger.error(f"下载ChromeDriver时出错: {e}")
        return None

class SimpleDeviceScraper:
    def __init__(self):
        """初始化爬虫"""
        self.driver = None
        self.all_data = []
        self.setup_driver()
    
    def setup_driver(self):
        """设置Chrome驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        try:
            # 尝试下载ChromeDriver
            driver_path = download_chromedriver()
            if driver_path and os.path.exists(driver_path):
                from selenium.webdriver.chrome.service import Service
                service = Service(driver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                # 使用系统默认的ChromeDriver
                self.driver = webdriver.Chrome(options=chrome_options)
            
            logger.info("Chrome驱动初始化成功")
        except Exception as e:
            logger.error(f"Chrome驱动初始化失败: {e}")
            print("请确保已安装Chrome浏览器，或手动下载ChromeDriver到当前目录")
            raise
    
    def scrape_data(self, url):
        """爬取数据"""
        try:
            logger.info(f"访问网站: {url}")
            self.driver.get(url)
            time.sleep(5)
            
            # 检查是否需要登录
            print("请在浏览器中完成登录（如果需要），然后按回车键继续...")
            input()
            
            page_num = 1
            all_headers = []
            
            while True:
                logger.info(f"正在爬取第 {page_num} 页...")
                time.sleep(2)
                
                # 查找表格
                tables = self.driver.find_elements(By.TAG_NAME, "table")
                if not tables:
                    logger.warning("未找到表格")
                    break
                
                # 使用第一个表格
                table = tables[0]
                
                # 提取表头
                if not all_headers:
                    header_elements = table.find_elements(By.CSS_SELECTOR, "thead tr th, tr:first-child th, tr:first-child td")
                    all_headers = [h.text.strip() for h in header_elements if h.text.strip()]
                    if not all_headers:
                        all_headers = [f"列{i+1}" for i in range(10)]
                    logger.info(f"表头: {all_headers}")
                
                # 提取数据行
                rows = table.find_elements(By.CSS_SELECTOR, "tbody tr, tr")
                page_data = []
                
                for row in rows:
                    cells = row.find_elements(By.CSS_SELECTOR, "td, th")
                    if cells:
                        row_data = [cell.text.strip() for cell in cells]
                        if row_data and any(cell for cell in row_data):
                            # 确保行数据长度与表头匹配
                            while len(row_data) < len(all_headers):
                                row_data.append("")
                            page_data.append(row_data[:len(all_headers)])
                
                if page_data:
                    self.all_data.extend(page_data)
                    logger.info(f"第 {page_num} 页提取到 {len(page_data)} 条数据")
                else:
                    logger.warning(f"第 {page_num} 页没有数据")
                
                # 查找下一页按钮
                next_buttons = self.driver.find_elements(By.XPATH, 
                    "//button[contains(text(), '下一页')] | //a[contains(text(), '下一页')] | //button[contains(@class, 'next')] | //a[contains(@class, 'next')]")
                
                next_clicked = False
                for button in next_buttons:
                    try:
                        if button.is_enabled() and button.is_displayed():
                            self.driver.execute_script("arguments[0].click();", button)
                            time.sleep(3)
                            page_num += 1
                            next_clicked = True
                            break
                    except:
                        continue
                
                if not next_clicked:
                    logger.info("没有找到可用的下一页按钮，爬取完成")
                    break
            
            return all_headers, self.all_data
            
        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return [], []
    
    def save_to_excel(self, headers, data, filename="设备明细列表.xlsx"):
        """保存数据到Excel文件"""
        try:
            if not data:
                logger.warning("没有数据可保存")
                return False
            
            df = pd.DataFrame(data, columns=headers if headers else [f"列{i+1}" for i in range(len(data[0]))])
            df.to_excel(filename, index=False, engine='openpyxl')
            logger.info(f"数据已保存到 {filename}，共 {len(data)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"保存Excel文件时出错: {e}")
            return False
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    url = "https://open.dianxinai.com/home?route=MyBill"
    
    scraper = SimpleDeviceScraper()
    
    try:
        headers, data = scraper.scrape_data(url)
        
        if data:
            success = scraper.save_to_excel(headers, data)
            if success:
                print(f"爬取完成！共获取 {len(data)} 条设备数据，已保存到 '设备明细列表.xlsx'")
            else:
                print("数据保存失败")
        else:
            print("未获取到任何数据")
    
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
