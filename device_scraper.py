#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备明细列表数据爬虫
爬取 https://open.dianxinai.com/home?route=MyBill 页面的设备明细数据
"""

import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeviceScraper:
    def __init__(self, headless=False):
        """初始化爬虫"""
        self.driver = None
        self.wait = None
        self.all_data = []
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """设置Chrome驱动"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("Chrome驱动初始化成功")
        except Exception as e:
            logger.error(f"Chrome驱动初始化失败: {e}")
            raise
    
    def login_if_needed(self):
        """检查是否需要登录，如果需要则等待用户手动登录"""
        try:
            # 检查是否存在登录相关元素
            login_elements = self.driver.find_elements(By.XPATH, "//input[@type='password'] | //button[contains(text(), '登录')] | //a[contains(text(), '登录')]")
            
            if login_elements:
                logger.info("检测到需要登录，请在浏览器中手动完成登录...")
                input("请在浏览器中完成登录后，按回车键继续...")
                time.sleep(2)
            
        except Exception as e:
            logger.warning(f"登录检查时出现异常: {e}")
    
    def find_device_table(self):
        """查找设备明细表格"""
        possible_selectors = [
            "table",
            ".table",
            "[class*='table']",
            "[class*='list']",
            "[class*='device']",
            ".ant-table-tbody",
            ".el-table__body",
            "tbody"
        ]
        
        for selector in possible_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    logger.info(f"找到可能的表格元素: {selector}")
                    return elements[0]
            except:
                continue
        
        return None
    
    def extract_table_data(self, table_element):
        """从表格元素中提取数据"""
        data = []
        try:
            # 尝试获取表头
            headers = []
            header_elements = table_element.find_elements(By.CSS_SELECTOR, "thead tr th, thead tr td, tr:first-child th, tr:first-child td")
            
            if not header_elements:
                # 如果没有找到表头，尝试从第一行获取
                first_row = table_element.find_element(By.CSS_SELECTOR, "tr")
                header_elements = first_row.find_elements(By.CSS_SELECTOR, "th, td")
            
            for header in header_elements:
                headers.append(header.text.strip())
            
            if not headers:
                headers = [f"列{i+1}" for i in range(10)]  # 默认列名
            
            logger.info(f"表头: {headers}")
            
            # 获取数据行
            rows = table_element.find_elements(By.CSS_SELECTOR, "tbody tr, tr")
            
            for row in rows:
                cells = row.find_elements(By.CSS_SELECTOR, "td, th")
                if cells:
                    row_data = []
                    for cell in cells:
                        row_data.append(cell.text.strip())
                    
                    if row_data and any(cell for cell in row_data):  # 确保行不为空
                        # 确保行数据长度与表头匹配
                        while len(row_data) < len(headers):
                            row_data.append("")
                        data.append(row_data[:len(headers)])
            
            logger.info(f"提取到 {len(data)} 行数据")
            return headers, data
            
        except Exception as e:
            logger.error(f"提取表格数据时出错: {e}")
            return [], []
    
    def find_pagination_info(self):
        """查找分页信息"""
        try:
            # 常见的分页元素选择器
            pagination_selectors = [
                ".pagination",
                ".ant-pagination",
                ".el-pagination",
                "[class*='page']",
                "[class*='Pagination']"
            ]
            
            for selector in pagination_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    logger.info(f"找到分页元素: {selector}")
                    return elements[0]
            
            return None
        except Exception as e:
            logger.error(f"查找分页信息时出错: {e}")
            return None
    
    def get_next_page_button(self):
        """获取下一页按钮"""
        next_button_selectors = [
            "//button[contains(text(), '下一页')]",
            "//a[contains(text(), '下一页')]",
            "//button[contains(@class, 'next')]",
            "//a[contains(@class, 'next')]",
            "//li[contains(@class, 'next')]/a",
            "//li[contains(@class, 'next')]/button",
            "//*[contains(@aria-label, 'Next')]",
            "//*[contains(@title, '下一页')]"
        ]
        
        for selector in next_button_selectors:
            try:
                elements = self.driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_enabled() and element.is_displayed():
                        return element
            except:
                continue
        
        return None
    
    def scrape_all_pages(self, url):
        """爬取所有页面的数据"""
        try:
            logger.info(f"开始访问: {url}")
            self.driver.get(url)
            time.sleep(3)
            
            # 检查是否需要登录
            self.login_if_needed()
            
            page_num = 1
            all_headers = []
            
            while True:
                logger.info(f"正在爬取第 {page_num} 页...")
                
                # 等待页面加载
                time.sleep(2)
                
                # 查找设备表格
                table = self.find_device_table()
                if not table:
                    logger.warning("未找到设备表格")
                    break
                
                # 提取当前页数据
                headers, page_data = self.extract_table_data(table)
                
                if not all_headers and headers:
                    all_headers = headers
                
                if page_data:
                    self.all_data.extend(page_data)
                    logger.info(f"第 {page_num} 页提取到 {len(page_data)} 条数据")
                else:
                    logger.warning(f"第 {page_num} 页没有数据")
                
                # 查找下一页按钮
                next_button = self.get_next_page_button()
                if next_button:
                    try:
                        self.driver.execute_script("arguments[0].click();", next_button)
                        time.sleep(3)
                        page_num += 1
                    except Exception as e:
                        logger.error(f"点击下一页失败: {e}")
                        break
                else:
                    logger.info("没有找到下一页按钮，爬取完成")
                    break
            
            return all_headers, self.all_data
            
        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return [], []
    
    def save_to_excel(self, headers, data, filename="device_data.xlsx"):
        """保存数据到Excel文件"""
        try:
            if not data:
                logger.warning("没有数据可保存")
                return False
            
            # 创建DataFrame
            df = pd.DataFrame(data, columns=headers if headers else [f"列{i+1}" for i in range(len(data[0]))])
            
            # 保存到Excel
            df.to_excel(filename, index=False, engine='openpyxl')
            logger.info(f"数据已保存到 {filename}，共 {len(data)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"保存Excel文件时出错: {e}")
            return False
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    url = "https://open.dianxinai.com/home?route=MyBill"
    
    scraper = DeviceScraper(headless=False)  # 设置为False以便手动登录
    
    try:
        headers, data = scraper.scrape_all_pages(url)
        
        if data:
            success = scraper.save_to_excel(headers, data, "设备明细列表.xlsx")
            if success:
                print(f"爬取完成！共获取 {len(data)} 条设备数据，已保存到 '设备明细列表.xlsx'")
            else:
                print("数据保存失败")
        else:
            print("未获取到任何数据")
    
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
