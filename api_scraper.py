#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于API的设备明细列表数据爬虫
专门针对 https://open.dianxinai.com 的设备数据API
"""

import requests
import pandas as pd
import time
import json
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class APIDeviceScraper:
    def __init__(self):
        """初始化API爬虫"""
        self.session = requests.Session()
        self.all_data = []
        self.total_pages = 0
        self.total_records = 0
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json'
        })
    
    def setup_session(self):
        """设置会话信息"""
        print("="*60)
        print("🔧 API爬虫配置")
        print("="*60)
        print()
        print("请提供以下信息以配置API请求：")
        print()
        
        # 获取API URL
        print("1. API URL:")
        print("   请在浏览器开发者工具中找到设备列表的API请求URL")
        api_url = input("   请输入完整的API URL: ").strip()
        
        if not api_url:
            print("❌ API URL不能为空")
            return False
        
        self.api_url = api_url
        
        # 获取Cookie
        print("\n2. Cookie信息:")
        print("   在开发者工具中找到API请求的Cookie")
        print("   右键请求 -> Copy -> Copy as cURL，然后提取Cookie部分")
        cookie = input("   请输入Cookie (可选，按回车跳过): ").strip()
        
        if cookie:
            self.session.headers['Cookie'] = cookie
        
        # 获取其他必要的请求头
        print("\n3. 其他请求头 (可选):")
        print("   如果API需要特殊的请求头，请提供")
        
        auth_header = input("   Authorization (可选): ").strip()
        if auth_header:
            self.session.headers['Authorization'] = auth_header
        
        csrf_token = input("   X-CSRF-Token (可选): ").strip()
        if csrf_token:
            self.session.headers['X-CSRF-Token'] = csrf_token
        
        referer = input("   Referer (可选): ").strip()
        if referer:
            self.session.headers['Referer'] = referer
        else:
            self.session.headers['Referer'] = 'https://open.dianxinai.com/'
        
        return True
    
    def test_api_connection(self, page=1):
        """测试API连接"""
        try:
            print(f"\n🔍 测试API连接 (第{page}页)...")
            
            # 构建请求参数
            params = {
                'current': page,
                'size': 20
            }
            
            response = self.session.get(self.api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('code') == 200 and 'data' in data:
                    records = data['data'].get('records', [])
                    total = data['data'].get('total', 0)
                    pages = data['data'].get('pages', 0)
                    
                    print(f"✓ API连接成功!")
                    print(f"✓ 总记录数: {total}")
                    print(f"✓ 总页数: {pages}")
                    print(f"✓ 当前页记录数: {len(records)}")
                    
                    if records:
                        print(f"✓ 示例数据字段: {list(records[0].keys())}")
                    
                    self.total_records = total
                    self.total_pages = pages
                    
                    return True
                else:
                    print(f"❌ API返回错误: {data}")
                    return False
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text[:500]}")
                return False
                
        except Exception as e:
            print(f"❌ API测试失败: {e}")
            return False
    
    def scrape_page(self, page):
        """爬取单页数据"""
        try:
            params = {
                'current': page,
                'size': 20
            }
            
            response = self.session.get(self.api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('code') == 200 and 'data' in data:
                    records = data['data'].get('records', [])
                    return records
                else:
                    logger.error(f"第{page}页API返回错误: {data}")
                    return []
            else:
                logger.error(f"第{page}页HTTP错误: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"第{page}页爬取失败: {e}")
            return []
    
    def scrape_all_pages(self, start_page=1, end_page=None):
        """爬取所有页面数据"""
        if end_page is None:
            end_page = self.total_pages
        
        print(f"\n🚀 开始爬取数据...")
        print(f"📄 页面范围: {start_page} - {end_page}")
        print(f"📊 预计记录数: {self.total_records}")
        print()
        
        success_count = 0
        failed_pages = []
        
        for page in range(start_page, end_page + 1):
            try:
                print(f"正在爬取第 {page}/{end_page} 页...", end=" ")
                
                records = self.scrape_page(page)
                
                if records:
                    self.all_data.extend(records)
                    success_count += 1
                    print(f"✓ 获取 {len(records)} 条记录")
                else:
                    failed_pages.append(page)
                    print(f"❌ 失败")
                
                # 显示进度
                if page % 50 == 0:
                    print(f"📈 进度: {page}/{end_page} ({page/end_page*100:.1f}%), 已获取 {len(self.all_data)} 条记录")
                
                # 请求间隔，避免过于频繁
                time.sleep(0.5)
                
            except KeyboardInterrupt:
                print(f"\n⚠️ 用户中断，已爬取 {len(self.all_data)} 条记录")
                break
            except Exception as e:
                logger.error(f"第{page}页处理异常: {e}")
                failed_pages.append(page)
        
        print(f"\n📊 爬取完成!")
        print(f"✓ 成功页面: {success_count}")
        print(f"❌ 失败页面: {len(failed_pages)}")
        print(f"📋 总记录数: {len(self.all_data)}")
        
        if failed_pages:
            print(f"⚠️ 失败页面: {failed_pages[:10]}{'...' if len(failed_pages) > 10 else ''}")
        
        return self.all_data
    
    def save_to_excel(self, filename="设备明细列表_完整数据.xlsx"):
        """保存数据到Excel"""
        try:
            if not self.all_data:
                print("❌ 没有数据可保存")
                return False
            
            # 创建DataFrame
            df = pd.DataFrame(self.all_data)
            
            # 添加爬取时间
            df['爬取时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 保存到Excel
            df.to_excel(filename, index=False, engine='openpyxl')
            
            print(f"✅ 数据已保存到: {filename}")
            print(f"📊 记录数量: {len(self.all_data)}")
            print(f"📋 字段列表: {list(df.columns)}")
            
            # 显示数据统计
            print(f"\n📈 数据统计:")
            print(f"   省份分布: {df['province'].value_counts().head().to_dict()}")
            print(f"   运营商分布: {df['ispName'].value_counts().to_dict()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存Excel失败: {e}")
            return False

def main():
    """主函数"""
    print("🕷️ 设备明细列表API爬虫")
    print("="*60)
    
    scraper = APIDeviceScraper()
    
    try:
        # 配置会话
        if not scraper.setup_session():
            return
        
        # 测试API连接
        if not scraper.test_api_connection():
            print("❌ API连接失败，请检查配置")
            return
        
        # 确认爬取范围
        print(f"\n📋 爬取配置:")
        print(f"   总页数: {scraper.total_pages}")
        print(f"   总记录数: {scraper.total_records}")
        
        start_page = input(f"\n起始页面 (默认1): ").strip()
        start_page = int(start_page) if start_page else 1
        
        end_page = input(f"结束页面 (默认{scraper.total_pages}): ").strip()
        end_page = int(end_page) if end_page else scraper.total_pages
        
        print(f"\n⚠️ 即将爬取第 {start_page} 到第 {end_page} 页，共 {end_page - start_page + 1} 页")
        confirm = input("确认开始爬取? (y/n): ").strip().lower()
        
        if confirm not in ['y', 'yes', '是']:
            print("已取消")
            return
        
        # 开始爬取
        data = scraper.scrape_all_pages(start_page, end_page)
        
        if data:
            # 保存数据
            scraper.save_to_excel()
            print(f"\n🎉 爬取完成! 共获取 {len(data)} 条设备数据")
        else:
            print("❌ 未获取到任何数据")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        logger.exception("详细错误信息:")

if __name__ == "__main__":
    main()
