# 设备明细列表爬虫

这个工具用于爬取 https://open.dianxinai.com/home?route=MyBill 页面的设备明细列表数据，并保存到Excel文件中。

## 🚀 快速开始

### 推荐方案：手动操作版本
由于浏览器驱动兼容性问题，推荐使用手动操作版本：

```bash
python manual_scraper.py
```

### 自动化版本（需要配置浏览器驱动）
如果您熟悉浏览器驱动配置，可以尝试：

```bash
python edge_scraper.py
```

## 📦 安装依赖

### 方法1：使用安装脚本（推荐）
```bash
python install_dependencies.py
```

### 方法2：手动安装
```bash
pip install -r requirements.txt
```

### 方法3：逐个安装
```bash
pip install selenium==4.15.0
pip install pandas==2.1.3
pip install openpyxl==3.1.2
pip install webdriver-manager==4.0.1
pip install beautifulsoup4
pip install requests
```

## 📋 使用方法

### 方案1：手动操作版本（推荐）

1. 运行手动爬虫：
   ```bash
   python manual_scraper.py
   ```

2. 按照提示操作：
   - 选择操作模式
   - 在浏览器中打开目标网站并登录
   - 使用开发者工具获取API数据
   - 将数据粘贴到程序中

### 方案2：自动化版本

1. 运行Edge爬虫：
   ```bash
   python edge_scraper.py
   ```

2. 程序会自动：
   - 打开Edge浏览器
   - 访问目标网站
   - 等待您手动登录
   - 自动爬取所有页面数据
   - 保存到Excel文件

## 📁 文件说明

- `manual_scraper.py` - **推荐** 手动操作版本爬虫
- `edge_scraper.py` - Edge浏览器自动化版本
- `device_scraper.py` - Chrome浏览器版本（可能有驱动问题）
- `test_edge.py` - Edge浏览器测试脚本
- `install_dependencies.py` - 依赖安装脚本
- `requirements.txt` - 依赖列表
- `README.md` - 使用说明

## ⚠️ 注意事项

1. **浏览器要求**:
   - 手动版本：任何现代浏览器
   - 自动化版本：需要Edge或Chrome浏览器
2. **网络连接**: 确保网络连接正常
3. **登录**: 网站需要登录，请准备好账号密码
4. **数据格式**: 程序会自动识别表格结构并保存为Excel格式

## 🔧 故障排除

### 1. 浏览器驱动问题
如果自动化版本无法启动浏览器：
- 使用手动版本：`python manual_scraper.py`
- 确保已安装Edge浏览器
- 检查网络连接

### 2. 找不到表格数据
- 确认已正确登录网站
- 检查页面是否完全加载
- 确认页面包含设备明细表格

### 3. 数据格式问题
- 手动版本支持JSON格式的API响应
- 如果数据格式特殊，可以联系技术支持

## 💡 使用技巧

### 手动版本操作技巧：
1. **获取API数据**：
   - 按F12打开开发者工具
   - 点击Network标签
   - 刷新页面或翻页
   - 找到返回设备数据的请求
   - 复制Response内容

2. **批量处理**：
   - 可以一次性复制多页数据
   - 程序支持逐页添加数据

### 自动化版本技巧：
1. **登录后操作**：
   - 程序会等待您手动登录
   - 登录完成后按回车继续

2. **翻页处理**：
   - 程序会自动查找下一页按钮
   - 如果自动翻页失败，可以手动翻页

## 📊 输出文件

爬取完成后会生成 `设备明细列表.xlsx` 文件，包含：
- 所有页面的设备数据
- 自动识别的列名
- 标准Excel格式，便于后续处理

## 🆘 获取帮助

如果遇到问题：
1. 首先尝试手动版本
2. 检查网络连接和登录状态
3. 查看程序输出的错误信息
4. 确认网站结构是否发生变化
