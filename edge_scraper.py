#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Edge浏览器的设备明细列表数据爬虫
"""

import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.options import Options
from selenium.webdriver.edge.service import Service
from webdriver_manager.microsoft import EdgeChromiumDriverManager
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EdgeDeviceScraper:
    def __init__(self):
        """初始化爬虫"""
        self.driver = None
        self.all_data = []
        self.setup_driver()
    
    def setup_driver(self):
        """设置Edge驱动"""
        edge_options = Options()
        edge_options.add_argument('--no-sandbox')
        edge_options.add_argument('--disable-dev-shm-usage')
        edge_options.add_argument('--disable-gpu')
        edge_options.add_argument('--window-size=1920,1080')
        # 保持浏览器打开，方便用户操作
        edge_options.add_experimental_option("detach", True)
        
        try:
            # 使用webdriver-manager自动管理Edge驱动
            service = Service(EdgeChromiumDriverManager().install())
            self.driver = webdriver.Edge(service=service, options=edge_options)
            logger.info("Edge驱动初始化成功")
        except Exception as e:
            logger.error(f"Edge驱动初始化失败: {e}")
            try:
                # 尝试使用系统默认的Edge驱动
                self.driver = webdriver.Edge(options=edge_options)
                logger.info("使用系统Edge驱动成功")
            except Exception as e2:
                logger.error(f"使用系统Edge驱动也失败: {e2}")
                print("请确保已安装Microsoft Edge浏览器")
                raise
    
    def wait_for_user_login(self):
        """等待用户手动登录"""
        print("\n" + "="*50)
        print("请在Edge浏览器中完成以下操作：")
        print("1. 如果需要登录，请手动登录")
        print("2. 确保已进入设备明细列表页面")
        print("3. 完成后，回到这里按回车键继续...")
        print("="*50)
        input()
        time.sleep(2)
    
    def find_table_data(self):
        """查找并提取表格数据"""
        try:
            # 多种表格选择器
            table_selectors = [
                "table",
                ".table",
                "[class*='table']",
                ".ant-table",
                ".el-table",
                "[class*='list']",
                "[class*='device']"
            ]
            
            table = None
            for selector in table_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    # 选择包含最多行的表格
                    best_table = None
                    max_rows = 0
                    for elem in elements:
                        rows = elem.find_elements(By.CSS_SELECTOR, "tr")
                        if len(rows) > max_rows:
                            max_rows = len(rows)
                            best_table = elem
                    
                    if best_table and max_rows > 1:  # 至少要有表头和一行数据
                        table = best_table
                        logger.info(f"找到表格，包含 {max_rows} 行")
                        break
            
            if not table:
                logger.warning("未找到合适的表格")
                return [], []
            
            # 提取表头
            headers = []
            header_selectors = [
                "thead tr th",
                "thead tr td", 
                "tr:first-child th",
                "tr:first-child td"
            ]
            
            for selector in header_selectors:
                header_elements = table.find_elements(By.CSS_SELECTOR, selector)
                if header_elements:
                    headers = [h.text.strip() for h in header_elements if h.text.strip()]
                    break
            
            if not headers:
                # 如果没有找到表头，使用默认列名
                first_row = table.find_element(By.CSS_SELECTOR, "tr")
                cells = first_row.find_elements(By.CSS_SELECTOR, "td, th")
                headers = [f"列{i+1}" for i in range(len(cells))]
            
            logger.info(f"表头: {headers}")
            
            # 提取数据行
            rows = table.find_elements(By.CSS_SELECTOR, "tbody tr, tr")
            data = []
            
            for i, row in enumerate(rows):
                cells = row.find_elements(By.CSS_SELECTOR, "td, th")
                if cells:
                    row_data = [cell.text.strip() for cell in cells]
                    # 跳过可能的表头行
                    if row_data and any(cell for cell in row_data) and not all(cell in headers for cell in row_data):
                        # 确保行数据长度与表头匹配
                        while len(row_data) < len(headers):
                            row_data.append("")
                        data.append(row_data[:len(headers)])
            
            logger.info(f"提取到 {len(data)} 行数据")
            return headers, data
            
        except Exception as e:
            logger.error(f"提取表格数据时出错: {e}")
            return [], []
    
    def find_next_page_button(self):
        """查找下一页按钮"""
        next_button_selectors = [
            "//button[contains(text(), '下一页')]",
            "//a[contains(text(), '下一页')]",
            "//button[contains(text(), '下页')]",
            "//a[contains(text(), '下页')]",
            "//button[contains(@class, 'next')]",
            "//a[contains(@class, 'next')]",
            "//li[contains(@class, 'next')]/a",
            "//li[contains(@class, 'next')]/button",
            "//*[contains(@aria-label, 'Next')]",
            "//*[contains(@title, '下一页')]",
            "//button[contains(@class, 'ant-pagination-next')]",
            "//li[contains(@class, 'ant-pagination-next')]/button"
        ]
        
        for selector in next_button_selectors:
            try:
                elements = self.driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_enabled() and element.is_displayed():
                        # 检查按钮是否被禁用
                        classes = element.get_attribute("class") or ""
                        if "disabled" not in classes.lower():
                            return element
            except:
                continue
        
        return None
    
    def scrape_all_pages(self, url):
        """爬取所有页面的数据"""
        try:
            logger.info(f"访问网站: {url}")
            self.driver.get(url)
            time.sleep(3)
            
            # 等待用户手动登录
            self.wait_for_user_login()
            
            page_num = 1
            all_headers = []
            
            while True:
                logger.info(f"正在爬取第 {page_num} 页...")
                time.sleep(2)
                
                # 提取当前页数据
                headers, page_data = self.find_table_data()
                
                if not all_headers and headers:
                    all_headers = headers
                
                if page_data:
                    self.all_data.extend(page_data)
                    logger.info(f"第 {page_num} 页提取到 {len(page_data)} 条数据")
                    print(f"第 {page_num} 页: 获取到 {len(page_data)} 条数据")
                else:
                    logger.warning(f"第 {page_num} 页没有数据")
                    print(f"第 {page_num} 页: 没有找到数据")
                
                # 查找下一页按钮
                next_button = self.find_next_page_button()
                if next_button:
                    try:
                        print(f"找到下一页按钮，准备翻页...")
                        # 滚动到按钮位置
                        self.driver.execute_script("arguments[0].scrollIntoView();", next_button)
                        time.sleep(1)
                        # 点击下一页
                        self.driver.execute_script("arguments[0].click();", next_button)
                        time.sleep(3)
                        page_num += 1
                    except Exception as e:
                        logger.error(f"点击下一页失败: {e}")
                        print("自动翻页失败，请手动翻页后按回车继续，或直接按回车结束爬取...")
                        user_input = input()
                        if user_input.strip().lower() in ['q', 'quit', 'exit', '退出']:
                            break
                        page_num += 1
                else:
                    logger.info("没有找到下一页按钮")
                    print("没有找到下一页按钮，爬取完成")
                    break
                
                # 防止无限循环，最多爬取100页
                if page_num > 100:
                    logger.warning("已爬取100页，停止爬取")
                    break
            
            return all_headers, self.all_data
            
        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return [], []
    
    def save_to_excel(self, headers, data, filename="设备明细列表.xlsx"):
        """保存数据到Excel文件"""
        try:
            if not data:
                logger.warning("没有数据可保存")
                return False
            
            # 创建DataFrame
            df = pd.DataFrame(data, columns=headers if headers else [f"列{i+1}" for i in range(len(data[0]))])
            
            # 保存到Excel
            df.to_excel(filename, index=False, engine='openpyxl')
            logger.info(f"数据已保存到 {filename}，共 {len(data)} 条记录")
            print(f"✓ 数据已保存到 {filename}")
            print(f"✓ 总共保存了 {len(data)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"保存Excel文件时出错: {e}")
            return False
    
    def close(self):
        """关闭浏览器"""
        try:
            print("\n是否关闭浏览器？(y/n，默认n): ", end="")
            close_browser = input().strip().lower()
            if close_browser in ['y', 'yes', '是']:
                if self.driver:
                    self.driver.quit()
                    logger.info("浏览器已关闭")
            else:
                logger.info("浏览器保持打开状态")
        except:
            if self.driver:
                self.driver.quit()

def main():
    """主函数"""
    print("设备明细列表爬虫 - Edge版本")
    print("="*40)
    
    url = "https://open.dianxinai.com/home?route=MyBill"
    
    scraper = EdgeDeviceScraper()
    
    try:
        headers, data = scraper.scrape_all_pages(url)
        
        if data:
            success = scraper.save_to_excel(headers, data)
            if success:
                print(f"\n🎉 爬取完成！")
                print(f"📊 共获取 {len(data)} 条设备数据")
                print(f"📁 已保存到 '设备明细列表.xlsx'")
            else:
                print("❌ 数据保存失败")
        else:
            print("❌ 未获取到任何数据，请检查：")
            print("   1. 网站是否正常访问")
            print("   2. 是否已正确登录")
            print("   3. 页面是否包含设备明细表格")
    
    except KeyboardInterrupt:
        print("\n用户中断了程序")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        print(f"❌ 程序执行出错: {e}")
    
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
